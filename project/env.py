import configat
from dotenv import load_dotenv


class _Choices:
    def __init__(self, values):
        self.values = values

    def __call__(self, value):
        if value in self.values:
            return value
        raise ValueError(f"Invalid value: {value}, expected one of {self.values}")


def load_env():
    env = configat.resolve(
        "@env:DJANGO_ENV",
        "development",
        cast=_Choices(["production", "development", "test"]),
    )
    load_dotenv(f".env.{env}")
    load_dotenv(f".env.{env}.local", override=True)
    return env
