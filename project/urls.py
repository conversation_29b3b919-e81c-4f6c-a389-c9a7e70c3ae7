from dataclasses import dataclass, field

import configat
from django.contrib import admin
from django.shortcuts import render
from django.urls import NoReverseMatch, include, path, reverse


@dataclass(kw_only=True)
class SiteMapNode:
    title: str
    urlname: str
    children: list["SiteMapNode"] = field(default_factory=list)


SITE_MAP_NODES = [
    SiteMapNode(title="Admin", urlname="admin:index"),
    SiteMapNode(
        title="Wallpapers External API",
        urlname="wallpapers-external:apidoc",
    ),
    SiteMapNode(
        title="Wallpapers Interal API",
        urlname="wallpapers-internal:apidoc",
    ),
    SiteMapNode(
        title="Upload Wallpapers",
        urlname="wallpapers-admin:upload-wallpapers",
    ),
]


def sitemap_view(request):
    def parse_sitemap(nodes: list[SiteMapNode]):
        if not nodes:
            return []

        def inner():
            for node in nodes:
                try:
                    url = reverse(node.urlname)
                except NoReverseMatch:
                    continue
                yield {
                    "title": node.title,
                    "url": url,
                    "children": list(parse_sitemap(node.children)),
                }

        return list(inner())

    return render(
        request,
        "index.html",
        context={"site_map": parse_sitemap(SITE_MAP_NODES)},
    )


SERVICE = configat.resolve("@env:DJANGO_SERVICE", None)
assert SERVICE in {None, "admin", "wallpapers-internal", "wallpapers-external"}

if SERVICE == "admin":
    urlpatterns = [
        path("", sitemap_view),
        path("admin/", admin.site.urls),
        path("wallpapers/", include("wallpapers.urls_admin")),
    ]
elif SERVICE == "wallpapers-internal":
    urlpatterns = [
        path("", include("wallpapers.urls_internal")),
    ]
elif SERVICE == "wallpapers-external":
    urlpatterns = [
        path("", include("wallpapers.urls_external")),
    ]
else:
    # development urls
    urlpatterns = [
        path("", sitemap_view),
        path("admin/", admin.site.urls),
        path("wi/", include("wallpapers.urls_internal")),
        path("wa/", include("wallpapers.urls_admin")),
        path("we/", include("wallpapers.urls_external")),
    ]
