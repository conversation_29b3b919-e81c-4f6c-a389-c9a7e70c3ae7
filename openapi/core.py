import functools
import typing
from inspect import iscoroutinefunction

import zangar as z
from django.http import HttpRequest, JsonResponse
from zangar.compilation import OpenAPI30Compiler

from .spec import ParameterObject, ResponseObject, specify


def response(*args, **kwargs):
    return specify(ResponseObject(*args, **kwargs))


class ThrowValue(Exception):
    def __init__(self, value):
        self.value = value


def catch(func):
    if iscoroutinefunction(func):

        @functools.wraps(func)
        async def awrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ThrowValue as e:
                return e.value

        return awrapper
    else:

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ThrowValue as e:
                return e.value

        return wrapper


def _compile_schema(schema: z.Schema):
    return OpenAPI30Compiler().compile(schema)


def path(name, /, *, schema: z.Schema, **kwargs):
    schema_object = _compile_schema(schema)
    parameter_object = ParameterObject(
        **{
            "name": name,
            "in": "path",
            "schema": schema_object,
            "required": True,
        },
        **kwargs,
    )

    def process_path_parameter(value, schema: z.Schema):
        try:
            return schema.parse(value)
        except z.ValidationError as e:
            raise ThrowValue(JsonResponse({"errors": e.format_errors()}, status=400))

    def decorator(func):
        if iscoroutinefunction(func):

            @specify(parameter_object)
            @catch
            @functools.wraps(func)
            async def awrapper(*a, **k):
                value = process_path_parameter(k.pop(name), schema)
                return await func(*a, **k, **{name: value})

            return awrapper
        else:

            @specify(parameter_object)
            @catch
            @functools.wraps(func)
            def wrapper(*a, **k):
                value = process_path_parameter(k.pop(name), schema)
                return func(*a, **k, **{name: value})

            return wrapper

    return decorator


_MISSING = object()


class Parameter:
    location: str

    def __init__(
        self,
        kwname: str | typing.Literal[False],
        /,
        *,
        name: str | None = None,
        schema: z.Schema,
        required=True,
        **kwargs,
    ):
        if name is None:
            if kwname is False:
                raise ValueError("If kwname is False, a name must be provided")
            name = kwname
        self.name: str = name
        self.__kwname: str | typing.Literal[False] = kwname
        self.__schema = schema
        self.__required = required
        self.__schema_object = _compile_schema(schema)
        self.__paramter_object = ParameterObject(
            **{
                "name": self.name,
                "in": self.location,
                "schema": self.__schema_object,
                "required": self.__required,
            },
            **kwargs,
        )

    def process_request(self, request: HttpRequest):
        raise NotImplementedError

    def __call__(self, func):
        def parse_request(request):
            param = self.process_request(request)
            if param is _MISSING and self.__required:
                raise ThrowValue(
                    JsonResponse(
                        {
                            "in": self.location,
                            "name": self.name,
                            "errors": "Missing required parameter",
                        },
                        status=400,
                    )
                )
            try:
                return self.__schema.parse(param)
            except z.ValidationError as e:
                raise ThrowValue(
                    JsonResponse(
                        {
                            "in": self.location,
                            "name": self.name,
                            "errors": e.format_errors(),
                        },
                        status=400,
                    )
                )

        if iscoroutinefunction(func):

            async def wrapper(_, request, *args, **kwargs):  # type: ignore
                if self.__kwname is not False:
                    kwargs[self.__kwname] = parse_request(request)
                return await func(_, request, *args, **kwargs)

        else:

            def wrapper(_, request, *args, **kwargs):
                if self.__kwname is not False:
                    kwargs[self.__kwname] = parse_request(request)
                return func(_, request, *args, **kwargs)

        rv = functools.wraps(func)(wrapper)
        rv = catch(rv)
        return specify(self.__paramter_object)(rv)


def query(
    kwname: str,
    /,
    *,
    name: str | None = None,
    schema: z.Schema,
    required=True,
    **kwargs,
):
    if name is None:
        name = kwname

    schema_object = _compile_schema(schema)
    parameter_object = ParameterObject(
        **{
            "name": name,
            "in": "query",
            "schema": schema_object,
            "required": required,
        },
        **kwargs,
    )

    def process_query_parameter(request: HttpRequest, schema: z.Schema):
        if schema_object["type"] == "object":
            # object type
            arg = request.GET
        elif schema_object["type"] == "array":
            arg = request.GET.getlist(name)
        else:
            # primitive type
            if name not in request.GET:
                if required:
                    raise ThrowValue(
                        JsonResponse(
                            {"errors": f"Missing query parameter: {name}"}, status=400
                        )
                    )
                return _MISSING
            arg = request.GET[name]
        try:
            return schema.parse(arg)
        except z.ValidationError as e:
            raise ThrowValue(
                JsonResponse(
                    {
                        "in": "query",
                        "name": name,
                        "errors": e.format_errors(),
                    },
                    status=400,
                )
            )

    def decorator(func):
        if iscoroutinefunction(func):

            @specify(parameter_object)
            @catch
            @functools.wraps(func)
            async def awrapper(self, request, *a, **k):
                value = process_query_parameter(request, schema)
                if value is not _MISSING:
                    k[kwname] = value
                return await func(self, request, *a, **k)

            return awrapper
        else:

            @specify(parameter_object)
            @catch
            @functools.wraps(func)
            def wrapper(self, request, *a, **k):
                value = process_query_parameter(request, schema)
                if value is not _MISSING:
                    k[kwname] = value
                return func(self, request, *a, **k)

            return wrapper

    return decorator


class Header(Parameter):
    location = "header"

    def process_request(self, request: HttpRequest):
        if self.name not in request.headers:
            return _MISSING
        return request.headers[self.name]


def header(*args, **kwargs):
    return Header(*args, **kwargs)
