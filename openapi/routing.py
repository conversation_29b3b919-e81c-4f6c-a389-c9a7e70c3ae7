import re

from django.http import JsonResponse
from django.urls import path
from django.views import View

from .spec import as_path_item_spec


class Route:
    __regex = re.compile(r"^\{.*\}$")

    def __init__(self, route: str):
        self.__parts = [x for x in route.split("/") if x]

    def django_url(self):
        parts = []
        for part in self.__parts:
            if self.__regex.match(part):
                parts.append("<" + part[1:-1] + ">")
            else:
                parts.append(part)
        return "/".join(parts)

    def openapi_path(self):
        return "/" + "/".join(self.__parts)


class Router:
    openapi_endpoint = "openapi.json"
    openapi_spec: dict = {}

    def __init__(self) -> None:
        self.__openapi_paths: dict[str, dict] = {}
        self.__urls = [path(self.openapi_endpoint, self.__openapi_json, name="openapi")]

    def add_url(self, route: str, view: type[View]):
        assert route.startswith("/"), "Path must start with '/'"
        r = Route(route)
        self.__openapi_paths[r.openapi_path()] = as_path_item_spec(view)
        self.__urls.append(path(r.django_url(), view.as_view(), name=view.__name__))

    def __openapi_json(self, request):
        prefix = request.path.replace(self.openapi_endpoint, "")[0:-1]
        spec = self.openapi_spec
        spec.setdefault("openapi", "3.0.3")
        spec.setdefault(
            "info",
            {
                "title": "API Document",
                "version": "0.1.0",
            },
        )
        if prefix:
            spec.setdefault("servers", [{"url": prefix}])
        spec.setdefault("paths", self.__openapi_paths)
        return JsonResponse(spec)

    @property
    def urls(self):
        return self.__urls
