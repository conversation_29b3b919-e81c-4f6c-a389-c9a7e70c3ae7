.DEFAULT_GOAL := help

ASYNCAPI-CLI-CHECK = command -v asyncapi > /dev/null 2>&1 || (echo "Installing @asyncapi/cli" && npm install -g @asyncapi/cli)

help: ## 显示所有命令
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | \
		awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

test: ## 执行测试
	@tox

coverage: ## 执行测试并生成覆盖率报告
	@coverage run -m pytest && coverage html

dev: ## 启动开发服务器
	@python manage.py runserver

docgen-config: ## 生成配置文档
	@python -c "import configat.docgen" > /dev/null 2>&1 || pip install "configat[docgen] @ git+https://github.com/Dog-Egg/configat.git"
	@python -m configat.docgen .