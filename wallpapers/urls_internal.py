from django.shortcuts import redirect, render
from django.urls import include, path, reverse

from openapi.routing import Router

from . import views


class MyRouter(Router):
    openapi_spec = {
        "info": {
            "title": "Wallpapers Internal API",
            "version": "0.1.0",
        }
    }


router = MyRouter()
router.add_url("/wallpapers", views.WallpaperListView)
router.add_url("/wallpapers/{key}/related", views.RelatedWallpapersAPI)
router.add_url("/categories/colors", views.ColorCategoryAPI)
router.add_url("/categories/tags", views.TagCategoryAPI)


def index(request):
    return redirect("wallpapers-internal:apidoc")


def apidoc(request):
    return render(
        request,
        "swagger-ui.html",
        context={
            "config": {"url": reverse("wallpapers-internal:openapi")},
        },
    )


app_name = "wallpapers-internal"
urlpatterns = [
    path("", index, name="index"),
    path("apidoc/", apidoc, name="apidoc"),
    path("", include(router.urls)),
]
