from django import forms
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest
from django.shortcuts import render
from django.urls import path, reverse_lazy

from wallpapers.services.rabbitmq import publish_wallpapers


class Textarea(forms.Textarea):
    def __init__(self, attrs) -> None:
        forms.Widget.__init__(self, attrs)


class UploadWallpapersForm(forms.Form):
    urls = forms.CharField(label="", widget=Textarea({"placeholder": "每行一条 URL"}))


@login_required(login_url=reverse_lazy("admin:login"))
def upload_wallpapers(request: HttpRequest):
    def process_urls(content: str):
        for line in content.splitlines():
            line = line.strip()
            if line:
                yield line

    if request.method == "POST":
        form = UploadWallpapersForm(request.POST)
        if form.is_valid():
            urls: str = form.cleaned_data["urls"]
            publish_wallpapers(list(process_urls(urls)))
            messages.success(request, "壁纸已进入队列")

    form = UploadWallpapersForm()
    return render(request, "upload_wallpapers.html", {"form": form})


app_name = "wallpapers-admin"
urlpatterns = [
    path("upload/wallpapers/", upload_wallpapers, name="upload-wallpapers"),
]
