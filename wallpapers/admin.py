from django.contrib import admin
from django.db.models.query import QuerySet
from django.http import HttpRequest
from django.utils.safestring import mark_safe

from wallpapers.utils.colors import ColorFamily

from . import models
from .utils.imgproxy import get_imgproxy_url


def _display_image(url):
    return mark_safe(
        f'<img src="{get_imgproxy_url(url, width=200, height=200)}" style="width: 100px; height: 100px; object-fit: contain"/>'
    )


class ColorFamilyFilter(admin.SimpleListFilter):
    title = "色系"
    parameter_name = "color_family"

    null = "~"

    def lookups(self, request: HttpRequest, model_admin: admin.ModelAdmin):
        return [(self.null, "未分类"), *ColorFamily.choices()]

    def queryset(self, request: HttpRequest, queryset: QuerySet):
        if self.value() == self.null:
            return queryset.filter(imagecolorfamily__isnull=True)
        if self.value():
            return queryset.filter(imagecolorfamily__color_family=self.value())
        return queryset


@admin.register(models.WallpaperImage)
class WallpaperImageAdmin(admin.ModelAdmin):
    list_display = [
        "image",
        "color_family",
        "clip_tags",
        "original_image",
    ]
    list_per_page = 20

    list_filter = [ColorFamilyFilter]

    @admin.display(description="图片")
    def image(self, obj):
        return _display_image(obj.url)

    @admin.display(description="原图")
    def original_image(self, obj):
        return mark_safe(f'<a href="{obj.url}" target="_blank">查看原图</a>')

    @admin.display(description="色系")
    def color_family(self, obj):
        try:
            return obj.imagecolorfamily.color_family
        except models.ImageColorFamily.DoesNotExist:
            return ""

    @admin.display(description="CLIP 标签")
    def clip_tags(self, obj: models.WallpaperImage):
        items = "".join(
            f"<li>{tag.name} ({tag.score})</li>"
            for tag in obj.cliptag_set.all()  # type: ignore
        )
        return mark_safe(f"<ul>{items}</ul>")

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return (
            super()
            .get_queryset(request)
            .select_related("imagecolorfamily")
            .prefetch_related("cliptag_set")
        )

    @admin.action(description="计算图片所属色系")
    def calculate_color_family_action(self, request, queryset):
        from wallpapers.services.rabbitmq import (
            publish_color_family_calculation,
        )

        publish_color_family_calculation(queryset)

    @admin.action(description="获取 CLIP 模型获得标签")
    def clip_tagging_action(self, request, queryset):
        from wallpapers.services.rabbitmq import (
            publish_clip_tagging,
        )

        publish_clip_tagging(queryset)

    actions = [
        calculate_color_family_action.__name__,
        clip_tagging_action.__name__,
    ]


@admin.register(models.ImageColorFamily)
class ImageColorFamilyAdmin(admin.ModelAdmin):
    list_display = ["wallpaper", "color_family"]


@admin.register(models.CLIPTag)
class CLIPTagAdmin(admin.ModelAdmin):
    class TopFilter(admin.SimpleListFilter):
        N = 10
        title = f"图片数量排名前 {N} 的标签"
        parameter_name = "top"

        def lookups(self, request: HttpRequest, model_admin: admin.ModelAdmin):
            from wallpapers.services.misc import get_top_tags

            return [(item["name"], item["name"]) for item in get_top_tags(self.N)]

        def queryset(self, request, queryset: QuerySet[models.CLIPTag]):
            value = self.value()
            if value:
                queryset = queryset.filter(name=value)
            return queryset

    list_filter = [TopFilter]
    list_display = ["image", "name", "score"]
    search_fields = ["name__exact"]

    @admin.display(description="图片")
    def image(self, obj: models.CLIPTag):
        return _display_image(obj.wallpaper.url)

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("wallpaper")


@admin.register(models.Uploader)
class UploaderAdmin(admin.ModelAdmin):
    pass


@admin.register(models.UploaderWallpaper)
class UploaderWallpaperAdmin(admin.ModelAdmin):
    pass
