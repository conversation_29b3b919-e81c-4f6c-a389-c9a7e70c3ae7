from django.http import HttpRequest
from django.shortcuts import redirect, render
from django.urls import include, path, reverse

from openapi.routing import Router

from . import views


class MyRouter(Router):
    openapi_spec = {
        "info": {
            "title": "Wallpapers External API",
            "version": "0.1.0",
        }
    }


router = MyRouter()
router.add_url("/image/{key}", views.ImageAPI)


def index(request):
    return redirect("wallpapers-external:apidoc")


def apidoc(request: HttpRequest):
    return render(
        request,
        "swagger-ui.html",
        context={
            "config": {"url": reverse("wallpapers-external:openapi")},
        },
    )


app_name = "wallpapers-external"
urlpatterns = [
    path("", index, name="index"),
    path("apidoc/", apidoc, name="apidoc"),
    path("", include(router.urls)),
]
