# Generated by Django 5.2.3 on 2025-06-16 02:58

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="WallpaperImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("url", models.URLField(verbose_name="图片地址")),
                ("format", models.CharField(max_length=10, verbose_name="格式")),
                ("width", models.IntegerField(verbose_name="宽度")),
                ("height", models.IntegerField(verbose_name="高度")),
                ("aspect_ratio", models.FloatField(verbose_name="宽高比")),
                ("pixels", models.IntegerField(verbose_name="像素数")),
                (
                    "filesize",
                    models.IntegerField(
                        help_text="单位为字节", verbose_name="文件大小"
                    ),
                ),
                (
                    "content_md5",
                    models.CharField(
                        max_length=32, unique=True, verbose_name="内容MD5"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "壁纸图片",
                "verbose_name_plural": "壁纸图片",
            },
        ),
    ]
