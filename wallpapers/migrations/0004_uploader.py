# Generated by Django 5.2.3 on 2025-07-13 10:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("wallpapers", "0003_cliptag"),
    ]

    operations = [
        migrations.CreateModel(
            name="Uploader",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="用户名")),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        unique=True,
                        verbose_name="邮箱",
                    ),
                ),
                (
                    "joined_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="注册时间"),
                ),
            ],
            options={
                "verbose_name": "壁纸上传者",
                "verbose_name_plural": "壁纸上传者",
            },
        ),
        migrations.CreateModel(
            name="UploaderWallpaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "uploader",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.uploader",
                        verbose_name="上传者",
                    ),
                ),
                (
                    "wallpaper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.wallpaperimage",
                        verbose_name="壁纸",
                    ),
                ),
            ],
            options={
                "verbose_name": "上传者壁纸",
                "verbose_name_plural": "上传者壁纸",
                "unique_together": {("uploader", "wallpaper")},
            },
        ),
    ]
