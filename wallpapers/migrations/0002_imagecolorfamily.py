# Generated by Django 5.2.3 on 2025-06-22 11:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("wallpapers", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ImageColorFamily",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "color_family",
                    models.CharField(
                        choices=[
                            ("red", "RED"),
                            ("pink", "PINK"),
                            ("orange", "ORANGE"),
                            ("yellow", "YELLOW"),
                            ("green", "GREEN"),
                            ("cyan", "CYAN"),
                            ("blue", "BLUE"),
                            ("purple", "PURPLE"),
                            ("black", "BLACK"),
                            ("white", "WHITE"),
                            ("gray", "GRAY"),
                        ],
                        max_length=10,
                        verbose_name="色系",
                    ),
                ),
                (
                    "wallpaper",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.wallpaperimage",
                        verbose_name="壁纸",
                    ),
                ),
            ],
            options={
                "verbose_name": "壁纸色系",
                "verbose_name_plural": "壁纸色系",
            },
        ),
    ]
