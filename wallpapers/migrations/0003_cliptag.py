# Generated by Django 5.2.3 on 2025-07-05 15:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("wallpapers", "0002_imagecolorfamily"),
    ]

    operations = [
        migrations.CreateModel(
            name="CLIPTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="标签名称")),
                ("score", models.FloatField(verbose_name="分数")),
                (
                    "wallpaper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.wallpaperimage",
                        verbose_name="壁纸",
                    ),
                ),
            ],
            options={
                "verbose_name": "CLIP 分类标签",
                "verbose_name_plural": "CLIP 分类标签",
                "unique_together": {("wallpaper", "name")},
            },
        ),
    ]
