from __future__ import annotations

from django.db import models

from wallpapers.utils.colors import ColorFamily


class WallpaperImage(models.Model):
    url = models.URLField(verbose_name="图片地址")
    format = models.CharField(max_length=10, verbose_name="格式")
    width = models.IntegerField(verbose_name="宽度")
    height = models.IntegerField(verbose_name="高度")
    aspect_ratio = models.FloatField(verbose_name="宽高比")
    pixels = models.IntegerField(verbose_name="像素数")
    filesize = models.IntegerField(verbose_name="文件大小", help_text="单位为字节")
    content_md5 = models.CharField(max_length=32, verbose_name="内容MD5", unique=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "壁纸图片"
        verbose_name_plural = "壁纸图片"

    cliptag_set: models.QuerySet[CLIPTag]
    uploaderwallpaper_set: models.QuerySet[UploaderWallpaper]


class Uploader(models.Model):
    name = models.CharField(max_length=100, verbose_name="用户名")
    email = models.EmailField(verbose_name="邮箱", null=True, blank=True, unique=True)
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name="注册时间")

    class Meta:
        verbose_name = "壁纸上传者"
        verbose_name_plural = "壁纸上传者"

    def __str__(self) -> str:
        return self.name


class UploaderWallpaper(models.Model):
    uploader = models.ForeignKey(
        Uploader, on_delete=models.CASCADE, verbose_name="上传者"
    )
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "上传者壁纸"
        verbose_name_plural = "上传者壁纸"
        unique_together = [("uploader", "wallpaper")]


class ImageColorFamily(models.Model):
    wallpaper = models.OneToOneField(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    color_family = models.CharField(
        max_length=10,
        verbose_name="色系",
        choices=ColorFamily.choices(),
    )

    class Meta:
        verbose_name = "壁纸色系"
        verbose_name_plural = "壁纸色系"


TAG_SCORE_THRESHOLD = 0.4  # 标签分数阈值，低于这个值将不准确


class CLIPTag(models.Model):
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    name = models.CharField(max_length=50, verbose_name="标签名称")
    score = models.FloatField(verbose_name="分数")

    class Meta:
        verbose_name = "CLIP 分类标签"
        verbose_name_plural = "CLIP 分类标签"
        unique_together = [("wallpaper", "name")]

    class _QualifiedMangar(models.Manager):
        def get_queryset(self):
            return super().get_queryset().filter(score__gte=TAG_SCORE_THRESHOLD)

    qualified: models.Manager[CLIPTag] = _QualifiedMangar()  # default mangar
    objects = models.Manager()
