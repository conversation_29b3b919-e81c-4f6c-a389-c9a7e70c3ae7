import os
from unittest import mock

import PIL.Image
import pytest
import schemathesis
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse

from project.wsgi import application
from wallpapers import models
from wallpapers.services import misc

User = get_user_model()

internal_schema = schemathesis.openapi.from_wsgi("/wi/openapi.json", application)


@internal_schema.parametrize()
@pytest.mark.django_db
def test_internal_api(case):
    case.call_and_validate()


class WallpapersAPITest(TestCase):
    def test_save_wallpaper(self):
        with open(
            os.path.join(os.path.dirname(__file__), "assets/fff.png"), "rb"
        ) as fp:
            content = fp.read()
            image = PIL.Image.open(fp)
            data = misc.WallpaperImageData(
                url="https://example.com/image.jpg",
                image=image,
                content=content,
            )
            with mock.patch(
                "wallpapers.services.misc.publish_color_family_calculation"
            ) as publish:
                obj = misc.save_wallpaper(data)
                args, kwargs = publish.call_args
                assert args[0] == [obj]

        self.assertEqual(obj.format, "png")
        self.assertEqual(obj.filesize, 1824)
        self.assertEqual(obj.width, 600)
        self.assertEqual(obj.height, 400)
        self.assertEqual(obj.pixels, 240000)
        self.assertEqual(obj.aspect_ratio, 1.5)

        # 二次保存，重复图片 - 应该返回相同的对象
        obj2 = misc.save_wallpaper(data)
        self.assertEqual(obj.pk, obj2.pk)  # 应该是同一个对象
        self.assertEqual(obj.content_md5, obj2.content_md5)  # MD5 应该相同


class TestWallpaperListView(TestCase):
    url = reverse("wallpapers-internal:WallpaperListView")

    def test_wallpapers_resize(self):
        response = self.client.get(self.url + "?size=12&size=aa")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json(),
            {
                "errors": [
                    {"loc": [0], "msgs": ["Invalid value"]},
                    {"loc": [1], "msgs": ["Invalid value"]},
                ],
                "in": "query",
                "name": "size",
            },
        )

    def test_wallpapers(self):
        wallpaper = models.WallpaperImage.objects.create(
            url="https://example.com/image.jpg",
            format="png",
            width=600,
            height=400,
            aspect_ratio=1.5,
            pixels=240000,
            filesize=1824,
        )
        response = self.client.get(self.url)
        # 只获取有上传者的壁纸
        assert response.json() == {
            "current_page": 1,
            "current_page_size": 10,
            "total": 0,
            "wallpapers": [],
        }

        uploader = models.Uploader.objects.create(name="Sam")
        models.UploaderWallpaper.objects.create(uploader=uploader, wallpaper=wallpaper)
        response = self.client.get(self.url)
        expected = {
            "current_page": 1,
            "current_page_size": 10,
            "total": 1,
            "wallpapers": [
                {
                    "content_md5": "",
                    "filesize": 1824,
                    "format": "png",
                    "height": 400,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/example.com/image.jpg",
                    },
                    "width": 600,
                    "uploader": {
                        "name": "Sam",
                    },
                },
            ],
        }
        assert response.json() == expected

        # 仅展示第一个上传者
        uploader2 = models.Uploader.objects.create(name="Tim")
        models.UploaderWallpaper.objects.create(uploader=uploader2, wallpaper=wallpaper)
        response = self.client.get(self.url)
        assert response.json() == expected


class WallpapersAdminTest(TestCase):
    username = "testuser"
    password = "testpass"

    def setUp(self):
        self.admin_user = User.objects.create_superuser(
            username=self.username, password=self.password, email=None
        )

    def test_upload_wallpapers(self):
        self.client.login(username=self.username, password=self.password)

        url = reverse("wallpapers-admin:upload-wallpapers")

        # GET
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # POST
        with mock.patch(
            "wallpapers.urls_admin.publish_wallpapers"
        ) as mock_publish_wallpapers:
            response = self.client.post(
                url,
                {
                    "urls": "https://example.com/image.jpg\n\r\nhttps://example.com/image2.jpg\r\n"
                },
            )

            # 验证 publish_messages 被调用，并且传入了正确的参数
            self.assertTrue(mock_publish_wallpapers.called)
            self.assertEqual(
                mock_publish_wallpapers.call_args.args,
                (
                    [
                        "https://example.com/image.jpg",
                        "https://example.com/image2.jpg",
                    ],
                ),
            )
            self.assertEqual(response.status_code, 200)
