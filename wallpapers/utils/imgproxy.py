import base64
import hashlib
import hmac
import typing
from urllib.parse import urljoin

import configat
from django.utils.functional import SimpleLazyObject

_IMGPROXY_URL = typing.cast(
    str, SimpleLazyObject(lambda: configat.resolve("@env:IMGPROXY_URL"))
)
_IMGPROXY_KEY: bytes = configat.resolve("@env:IMGPROXY_KEY", None, cast=bytes.fromhex)
_IMGPROXY_SALT: bytes = configat.resolve("@env:IMGPROXY_SALT", None, cast=bytes.fromhex)


def get_imgproxy_url(source_url: str, width: int = 0, height: int = 0) -> str:
    """
    生成 imgproxy 的 URL

    如何同时设置 width 和 height，则取最小值进行等比例缩放。
    """
    parts = [
        "mb:" + str(50 * 1024),
        "f:jpg",
    ]

    # resize
    if width != 0 or height != 0:
        parts.append(f"rs:fit:{width}:{height}")

    parts.extend(["plain", source_url])
    path = "/" + "/".join(parts)

    signature = _get_signture(path)

    return urljoin(_IMGPROXY_URL, f"{signature}{path}")


def _get_signture(path: str) -> str:
    if _IMGPROXY_KEY is None or _IMGPROXY_SALT is None:
        return "_"
    digest = hmac.new(
        _IMGPROXY_KEY,
        msg=_IMGPROXY_SALT + path.encode(),
        digestmod=hashlib.sha256,
    ).digest()
    return base64.urlsafe_b64encode(digest).rstrip(b"=").decode()
