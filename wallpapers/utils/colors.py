from __future__ import annotations

import colorsys
import enum
import typing
from collections import defaultdict

from PIL import Image


def get_color_family_historgram(
    content: typing.IO[bytes],
) -> dict[ColorFamily, float]:
    img = Image.open(content).convert("RGB")
    img = img.resize((100, 100))
    total_pixels = img.width * img.height

    histogram = defaultdict(int)
    for x in range(img.width):
        for y in range(img.height):
            r, g, b = typing.cast(tuple, img.getpixel((x, y)))
            h, s, v = colorsys.rgb_to_hsv(r / 255, g / 255, b / 255)
            histogram[_classify_hsv_simple(h, s, v)] += 1
    return {k: v / total_pixels for k, v in histogram.items()}


@enum.unique
class ColorFamily(enum.StrEnum):
    RED = "red", "#ff0000"
    PINK = "pink", "#ffc0cb"
    ORANGE = "orange", "#ffa500"
    YELLOW = "yellow", "#ffff00"
    GREEN = "green", "#00ff00"
    CYAN = "cyan", "#00ffff"
    BLUE = "blue", "#0000ff"
    PURPLE = "purple", "#800080"
    BLACK = "black", "#000000"
    WHITE = "white", "#ffffff"
    GRAY = "gray", "#808080"

    RGB: str
    label: str | None

    def __new__(cls, value, RGB, label: str | None = None):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.label = label
        obj.RGB = RGB
        return obj

    @classmethod
    def choices(cls):
        return [(k.value, k.label or k.name) for k in cls]

    def asdict(self):
        return {
            "value": self.value,
            "RGB": self.RGB,
        }


def _classify_hsv_simple(h: float, s: float, v: float) -> ColorFamily:
    """
    简化版颜色分类：红(粉)、橙、黄、绿、青、蓝、紫、黑、白、灰

    :param h: 色相 H(0.0 ~ 1.0)
    :param s: 饱和度 S(0.0 ~ 1.0)
    :param v: 明度 V(0.0 ~ 1.0)
    :returns:   主色系名称(str)
    """
    if v < 0.2:
        return ColorFamily.BLACK
    elif s < 0.15:
        if v > 0.9:
            return ColorFamily.WHITE
        else:
            return ColorFamily.GRAY

    h_deg = h * 360

    if h_deg < 20 or h_deg >= 340:
        if s < 0.5:
            return ColorFamily.PINK
        return ColorFamily.RED
    elif h_deg < 45:
        return ColorFamily.ORANGE
    elif h_deg < 70:
        return ColorFamily.YELLOW
    elif h_deg < 160:
        return ColorFamily.GREEN
    elif h_deg < 200:
        return ColorFamily.CYAN
    elif h_deg < 270:
        return ColorFamily.BLUE
    else:  # 270–339
        return ColorFamily.PURPLE


def get_color_family(image: typing.IO[bytes]) -> ColorFamily | None:
    color_families = get_color_family_historgram(image)
    # 取颜色占比大于 %N 的色系，从大到小排序
    results = sorted(
        filter(lambda x: x[1] >= 0.55, color_families.items()),
        key=lambda x: x[1],
        reverse=True,
    )
    if results:
        color_family, ratio = results[0]
        return color_family
