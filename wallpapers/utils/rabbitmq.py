import json
import logging
from typing import Any, Callable, Dict

import pika
from pika.adapters.blocking_connection import BlockingChannel
from pika.spec import Basic, BasicProperties

logger = logging.getLogger(__name__)


class RabbitMQConsumer:
    """
    RabbitMQ 消费者类

    支持传入自定义的处理函数，用于处理接收到的消息
    """

    connection: pika.BlockingConnection
    channel: BlockingChannel

    def __init__(self, url: str):
        """初始化消费者"""
        self.url = url

    def connect(self) -> None:
        """建立 RabbitMQ 连接"""
        parameters = pika.URLParameters(self.url)
        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()
        # 设置 QoS，一次只处理一条消息
        self.channel.basic_qos(prefetch_count=1)

    def on_message(self, queue: str, callback: Callable[[Dict[str, Any]], None]):
        # 声明队列（如果不存在则创建）
        self.channel.queue_declare(queue=queue, durable=True)

        def message_callback(
            ch: BlockingChannel,
            method: Basic.Deliver,
            properties: BasicProperties,
            body: bytes,
        ):
            try:
                # 解析消息
                message = json.loads(body.decode("utf-8"))

                # 调用处理函数
                callback(message)

                # 确认消息已处理
                ch.basic_ack(delivery_tag=method.delivery_tag)

            except Exception:
                # 拒绝消息，并将消息重新入队
                logger.exception(f"处理消息时出错: {message}")
                ch.basic_nack(delivery_tag=method.delivery_tag)

        self.channel.basic_consume(queue=queue, on_message_callback=message_callback)

    def start_consuming(self) -> None:
        """启动事件循环，等待并执行回调"""
        self.channel.start_consuming()

    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.channel.stop_consuming()
        self.connection.close()
