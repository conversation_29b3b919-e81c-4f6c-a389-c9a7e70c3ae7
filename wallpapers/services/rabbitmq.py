import json
import logging
from typing import Callable, Iterable, TypeVar

import configat
import pika
import zangar as z
from django.db.models import QuerySet
from pika.channel import Channel

from wallpapers import models

_logger = logging.getLogger(__name__)

_RABBITMQ_URL = configat.resolve("@env:RABBITMQ_URL")
_RABBITMQ_WALLPAPERS_INBOUND_QUEUE = configat.resolve(
    "@env:RABBITMQ_WALLPAPERS_INBOUND_QUEUE"
)
_RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE = configat.resolve(
    "@env:RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE"
)

_RABBITMQ_COOKNIFE_URL = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_URL", help="cooknife 服务的 RabbitMQ 连接地址"
)
_RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE", help="cooknife 服务的 CLIP 标签队列"
)
_RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE",
    help="cooknife 服务的 CLIP 标签回复队列",
)


def publish_clip_tagging(queryset: QuerySet[models.WallpaperImage]) -> None:
    """发布使用 CLIP 模型获得标签的任务"""
    parameter = pika.URLParameters(_RABBITMQ_COOKNIFE_URL)
    connection = pika.BlockingConnection(parameter)
    channel = connection.channel()
    queue = _RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE
    channel.queue_declare(queue=queue, durable=True)
    for item in queryset:
        channel.basic_publish(
            exchange="",
            routing_key=queue,
            body=json.dumps({"imageUrl": item.url}),
            properties=pika.BasicProperties(
                delivery_mode=2,  # 消息持久化
                correlation_id=str(item.pk),
                reply_to=_RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE,
            ),
        )


def publish_color_family_calculation(queryset: Iterable[models.WallpaperImage]):
    """发布计算色系的任务"""
    parameter = pika.URLParameters(_RABBITMQ_URL)
    connection = pika.BlockingConnection(parameter)
    channel = connection.channel()
    queue = _RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE
    channel.queue_declare(queue, durable=True)
    for item in queryset:
        channel.basic_publish(
            "",
            queue,
            body=json.dumps({"wallpaper_id": item.pk}),
            properties=pika.BasicProperties(
                delivery_mode=2,  # 消息持久化
            ),
        )


def publish_wallpapers(urls: Iterable[str]):
    """发布壁纸到队列"""
    parameter = pika.URLParameters(_RABBITMQ_URL)
    connection = pika.BlockingConnection(parameter)
    channel = connection.channel()
    queue = _RABBITMQ_WALLPAPERS_INBOUND_QUEUE
    channel.queue_declare(queue, durable=True)
    for url in urls:
        channel.basic_publish(
            "",
            queue,
            body=json.dumps({"url": url}),
            properties=pika.BasicProperties(
                delivery_mode=2,  # 消息持久化
            ),
        )


T = TypeVar("T")


class _Consumer:
    def __init__(self, url: str):
        parameter = pika.URLParameters(url)
        self.connection = pika.BlockingConnection(parameter)
        self.channel = self.connection.channel()
        self.channel.basic_qos(prefetch_count=1)

    def on_message(
        self,
        *,
        queue: str,
        message_schema: z.Schema[T],
        callback: Callable[[T, pika.BasicProperties], None],
    ):
        def on_message_callback(ch: Channel, method, properties, body):
            try:
                message = z.transform(json.loads).relay(message_schema).parse(body)
            except z.ValidationError as e:
                _logger.warning(
                    {
                        "queue": queue,
                        "message": body,
                        "errors": e.format_errors(),
                    }
                )
                ch.basic_nack(delivery_tag=method.delivery_tag)
                return
            callback(message, properties)
            ch.basic_ack(delivery_tag=method.delivery_tag)

        self.channel.queue_declare(queue, durable=True)
        self.channel.basic_consume(
            queue=queue,
            on_message_callback=on_message_callback,
        )

    def start_consuming(self):
        self.channel.start_consuming()

    def __enter__(self):
        return self

    def __exit__(self, *args, **kwargs):
        self.connection.__exit__(*args, **kwargs)


def consume_default_messages():
    """消费默认 vhost 的消息队列，包括壁纸下载和色系计算"""

    def callback_wallpapers(message, properties):
        from wallpapers.services.misc import (
            download_image,
            save_wallpaper,
            random_uploader,
        )

        image = download_image(message["url"])
        width, height = image.image.size
        if width * height < 720 * 1280:  # 最小壁纸要求尺寸
            _logger.warning(f"壁纸尺寸过小: {width}x{height}")
            return
        save_wallpaper(image, uploader=random_uploader())

    def callback_color_family(message, properties):
        from wallpapers.services.misc import color_family_job

        color_family_job(message["wallpaper_id"])

    with _Consumer(_RABBITMQ_URL) as consumer:
        consumer.on_message(
            queue=_RABBITMQ_WALLPAPERS_INBOUND_QUEUE,
            message_schema=z.struct({"url": z.str()}),
            callback=callback_wallpapers,
        )
        consumer.on_message(
            queue=_RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE,
            message_schema=z.struct({"wallpaper_id": z.int()}),
            callback=callback_color_family,
        )
        consumer.start_consuming()


def consume_cooknife_messages():
    def callback(message, properties: pika.BasicProperties):
        pk = properties.correlation_id
        try:
            wallpaper = models.WallpaperImage.objects.get(pk=pk)
        except models.WallpaperImage.DoesNotExist:
            _logger.warning("壁纸不存在: %s", pk)
            return
        for tag in message["tags"]:
            models.CLIPTag.objects.update_or_create(
                wallpaper=wallpaper,
                name=tag["name"],
                defaults={"score": tag["score"]},
            )

    with _Consumer(_RABBITMQ_COOKNIFE_URL) as consumer:
        consumer.on_message(
            queue=_RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE,
            message_schema=z.struct(
                {
                    "tags": z.list(
                        z.struct(
                            {
                                "name": z.str(),
                                "score": z.float(),
                            }
                        )
                    ),
                }
            ),
            callback=callback,
        )
        consumer.start_consuming()
