import configat

RABBITMQ_URL = configat.resolve("@env:RABBITMQ_URL")
RABBITMQ_WALLPAPERS_INBOUND_QUEUE = configat.resolve(
    "@env:RABBITMQ_WALLPAPERS_INBOUND_QUEUE"
)
RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE = configat.resolve(
    "@env:RABBITMQ_WALLPAPERS_COLOR_FAMILY_QUEUE"
)

RABBITMQ_COOKNIFE_URL = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_URL", help="cooknife 服务的 RabbitMQ 连接地址"
)
RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_CLIP_TAGGING_QUEUE", help="cooknife 服务的 CLIP 标签队列"
)
RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE = configat.resolve(
    "@env:RABBITMQ_COOKNIFE_CLIP_TAGGING_REPLY_QUEUE",
    help="cooknife 服务的 CLIP 标签回复队列",
)
