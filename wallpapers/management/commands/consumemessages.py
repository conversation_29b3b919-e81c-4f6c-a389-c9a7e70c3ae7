import threading

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "启动 RabbitMQ 消费者，处理壁纸保存任务"

    def handle(self, *args, **options):
        from wallpapers.services.rabbitmq import (
            consume_cooknife_messages,
            consume_default_messages,
        )

        # 使用多线程并发
        th1 = threading.Thread(target=consume_default_messages)
        th2 = threading.Thread(target=consume_cooknife_messages)
        th1.start()
        th2.start()
        th1.join()
        th2.join()
